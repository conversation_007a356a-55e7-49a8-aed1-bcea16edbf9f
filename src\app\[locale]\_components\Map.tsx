import { GoogleMapsEmbed } from "@next/third-parties/google";
import { cn } from "@/lib/utils";
import { appEnv } from "@/env";
import { LocaleType } from "@/i18n/routing";

type Props = {
  className?: string;
  locale?: LocaleType;
};

const Map = ({ className, locale }: Props) => {
  return (
    <section className={cn("bg-background w-full", className)}>
      <GoogleMapsEmbed
        apiKey={appEnv.GOOGLE_MAPS_API_KEY || ""}
        height={400}
        width="100%"
        mode="place"
        zoom="19"
        q="Rose+Palhares+Ready+to+Wear"
        maptype="roadmap"
        loading="lazy"
        allowfullscreen={false}
        style="border: none; display: block;"
        language={locale || ""}
      />
    </section>
  );
};

export default Map;
