import { getTranslations } from "next-intl/server";
import { <PERSON> } from "@/i18n/navigation";
import { buttonVariants } from "@/components/ui/button";
import { links } from "@/app/[locale]/links";
import LanguageSelector from "@/app/[locale]/_components/LanguageSelector";
import Logo from "@/assets/imgs/logo.svg";
import { cn } from "@/lib/utils";
import { Separator } from "./ui/separator";
import { Fragment } from "react";

const Header = async () => {
  const t = await getTranslations("link");

  return (
    <header className="z-50 w-full border-b bg-white">
      <section className="container-x flex flex-col items-center gap-4 py-4 sm:!pl-0">
        <div className="flex w-full items-center justify-between gap-y-4 py-2 max-sm:flex-col sm:py-4">
          <span className="bg-primary block h-5 w-15" />
          <nav className="flex items-center gap-4">
            <ul className="flex items-center">
              {Object.entries(links).map(([key, link]) => (
                <Fragment key={key}>
                  <Link
                    key={key}
                    href={link.href || "#"}
                    className={cn(buttonVariants({ variant: "link" }))}
                  >
                    {t(link.label)}
                  </Link>
                  <Separator
                    orientation="vertical"
                    className="bg-primary block min-h-5 min-w-[0.1rem]"
                  />
                </Fragment>
              ))}
            </ul>
            <LanguageSelector />
          </nav>
        </div>
        <Link href="/" className="block flex-shrink-0 sm:pl-20">
          <Logo className="h-auto w-full max-w-80" />
        </Link>
      </section>
    </header>
  );
};

export default Header;
