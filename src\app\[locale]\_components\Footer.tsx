import { getTranslations } from "next-intl/server";
import LogoAtelier from "@/assets/imgs/logo-atelier.svg";
import LogoReadyToWear from "@/assets/imgs/logo-ready-to-wear.svg";
import LogoSouDessas from "@/assets/imgs/logo-sou-dessas.svg";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const Footer = async ({ className }: Props) => {
  const t = await getTranslations("footer");

  const logos = [
    {
      name: "ready-to-wear",
      component: LogoReadyToWear,
    },
    {
      name: "atelier",
      component: LogoAtelier,
    },
    {
      name: "sou-dessas",
      component: LogoSouDessas,
    },
  ];

  return (
    <footer className="bg-primary text-muted-foreground">
      <section
        className={cn(
          "container-x flex flex-col gap-16 pt-12 pb-6 max-sm:items-center",
          className,
        )}
      >
        <ul className="grid gap-8 sm:grid-cols-3">
          {logos.map((logo) => (
            <li key={logo.name}>
              <logo.component className="h-full w-full max-sm:max-w-60" />
            </li>
          ))}
        </ul>
        <p>{t("copyright")}</p>
      </section>
    </footer>
  );
};

export default Footer;
