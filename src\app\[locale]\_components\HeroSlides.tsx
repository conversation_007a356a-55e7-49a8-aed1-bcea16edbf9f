import { getTranslations } from "next-intl/server";
import React from "react";
import HeroImg1 from "@/assets/imgs/hero-1.jpg";
import HeroImg2 from "@/assets/imgs/hero-2.jpg";
import SuperBrand from "@/assets/imgs/super-brands.svg";
import * as z from "zod/v4-mini";
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
} from "@/components/ui/carousel";
import Image from "next/image";
import { Button } from "@/components/ui/button";

const slideImages = {
  "hero-1": HeroImg1,
  "hero-2": HeroImg2,
} as const;

const slideSchema = z.object({
  title: z.array(z.string()),
  image: z.enum(Object.keys(slideImages) as [keyof typeof slideImages]),
});

type Props = {};

const HeroSlides = async (props: Props) => {
  const t = await getTranslations("hero");
  const tCommon = await getTranslations("common");

  const slides = z
    .array(slideSchema)
    .parse(t.raw("slides"))
    .map((slide) => ({
      title: slide.title,
      image: slideImages[slide.image],
    }));

  return (
    <Carousel className="relative grid">
      <CarouselContent className="ml-0">
        {slides.map((s, index) => (
          <CarouselItem
            key={index}
            className="grid-stack relative grid h-auto w-full items-end pl-0"
          >
            <Image
              src={s.image}
              alt={s.title.join(" ")}
              className="stack-item max-h-[150vh] w-full self-stretch object-cover object-top"
              quality={90}
            />
            <div className="container-full stack-item text-white uppercase">
              <h3 className="text-xl text-shadow-black/20 text-shadow-lg sm:text-3xl">
                {s.title[0]}
              </h3>
              <h1 className="text-4xl text-shadow-black/20 text-shadow-lg sm:text-8xl">
                {s.title[1]}
              </h1>
              <SuperBrand className="mt-36 ml-auto h-auto w-24 sm:w-36" />
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselDots className="absolute bottom-20 left-1/2 z-10 -translate-x-1/2" />
      <Button
        size="xl"
        className="bg-primary/50 absolute bottom-0 left-[20%] w-fit translate-y-1/2 pt-2 leading-none uppercase"
      >
        {tCommon("visitUs")}
      </Button>
    </Carousel>
  );
};

export default HeroSlides;
