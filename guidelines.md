# Folder Structure

- the folder structure should be organized in features, the related components should be in the same folder as the feature.
- the components/ui should be only for shadcn components.

# Components

- the component must be arrow functions and try to make the server side components as much as possible.
- always use the `cn` function to merge the classes.
- always try to use the shadcn components as much as possible.
- prefer to use the `gap` utility from tailwind instead of `space`.

# Translation

- try to use server side translation as much as possible.
- try not duplicate the translation keys, instead add them to `common` field.
